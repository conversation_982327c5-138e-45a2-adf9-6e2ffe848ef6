---
import Layout from '../layouts/Layout.astro';

const title = "Pay-As-You-Go QR Analytics - No Subscriptions, Just Results | QRAnalytica";
const description = "Revolutionary pay-as-you-go QR code analytics. Buy credits when you need them, use them when you want. Professional plan with 7-day free trial and one-time $99 payment. No recurring fees ever.";
const canonicalURL = "https://qranalytica.com/pricing";

// Structured Data for Pricing
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "QRAnalytica",
  "description": "Pay-as-you-go QR code analytics platform with no subscriptions",
  "offers": [
    {
      "@type": "Offer",
      "name": "Pay-As-You-Go Credits",
      "price": "0.10",
      "priceCurrency": "USD",
      "description": "Flexible credit system - pay only for what you use"
    },
    {
      "@type": "Offer",
      "name": "Professional Plan",
      "price": "99",
      "priceCurrency": "USD",
      "description": "Complete QR analytics suite with 7-day free trial, one-time payment"
    },
    {
      "@type": "Offer",
      "name": "Enterprise Plan",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Custom solutions for large organizations"
    }
  ]
};
---

<Layout title={title} description={description}>
  <!-- Structured Data -->
  <script type="application/ld+json" set:html={JSON.stringify(structuredData)} />

  <!-- Hero Section -->
  <section class="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
    <div class="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>

    <div class="max-w-6xl mx-auto px-6 text-center">
      <!-- Badge -->
      <div class="mb-8">
        <span class="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-700 text-sm font-medium border border-green-200">
          Simple Pay-As-You-Go Pricing
        </span>
      </div>

      <!-- Main Headline -->
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
        Buy Credits,
        <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Create QR Codes</span>
      </h1>

      <p class="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
        Simple credit-based pricing. No subscriptions, no monthly fees.
        Buy credits when you need them, use them when you want.
      </p>

      <!-- Simple Pricing Display -->
      <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 max-w-3xl mx-auto">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">How It Works</h2>

        <div class="grid md:grid-cols-2 gap-8 mb-8">
          <div class="text-center p-6 bg-blue-50 rounded-xl">
            <div class="text-4xl font-bold text-blue-600 mb-2">$0.10</div>
            <p class="text-gray-700 font-medium">per Dynamic QR Code</p>
            <p class="text-sm text-gray-500 mt-2">Create trackable QR codes with analytics</p>
          </div>
          <div class="text-center p-6 bg-purple-50 rounded-xl">
            <div class="text-4xl font-bold text-purple-600 mb-2">$0.01</div>
            <p class="text-gray-700 font-medium">per 100 extra scans</p>
            <p class="text-sm text-gray-500 mt-2">Add more scan capacity when needed</p>
          </div>
        </div>

        <div class="bg-gray-50 rounded-xl p-6">
          <div class="grid md:grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-green-600 text-xl mb-2">✓</div>
              <p class="text-sm font-medium text-gray-700">Credits Never Expire</p>
            </div>
            <div>
              <div class="text-green-600 text-xl mb-2">✓</div>
              <p class="text-sm font-medium text-gray-700">No Monthly Fees</p>
            </div>
            <div>
              <div class="text-green-600 text-xl mb-2">✓</div>
              <p class="text-sm font-medium text-gray-700">Use Anytime</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Credit Packages -->
  <section class="py-20 bg-white">
    <div class="max-w-5xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Credit Packages
        </h2>
        <p class="text-xl text-gray-600">
          Buy in bulk and save. All packages include the same features.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        <!-- Small Pack -->
        <div class="bg-white rounded-2xl border-2 border-gray-200 p-8 text-center hover:border-blue-300 transition-colors">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Small</h3>
          <div class="mb-6">
            <div class="text-4xl font-bold text-gray-900 mb-2">$10</div>
            <p class="text-gray-600">100 Credits</p>
            <p class="text-sm text-gray-500 mt-1">$0.10 per credit</p>
          </div>

          <div class="mb-8">
            <p class="text-gray-700 mb-4">Perfect for:</p>
            <ul class="text-sm text-gray-600 space-y-2">
              <li>• Small campaigns</li>
              <li>• Testing QR codes</li>
              <li>• Personal projects</li>
            </ul>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-800 transition-colors">
            Buy $10 Package
          </a>
        </div>

        <!-- Medium Pack -->
        <div class="bg-white rounded-2xl border-2 border-blue-300 p-8 text-center relative transform scale-105 shadow-lg">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
          </div>

          <h3 class="text-2xl font-bold text-gray-900 mb-4">Medium</h3>
          <div class="mb-6">
            <div class="text-4xl font-bold text-blue-600 mb-2">$45</div>
            <p class="text-gray-600">500 Credits</p>
            <p class="text-sm text-green-600 font-medium mt-1">$0.09 per credit • Save 10%</p>
          </div>

          <div class="mb-8">
            <p class="text-gray-700 mb-4">Perfect for:</p>
            <ul class="text-sm text-gray-600 space-y-2">
              <li>• Business campaigns</li>
              <li>• Marketing teams</li>
              <li>• Regular QR usage</li>
            </ul>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
            Buy $45 Package
          </a>
        </div>

        <!-- Large Pack -->
        <div class="bg-white rounded-2xl border-2 border-purple-200 p-8 text-center hover:border-purple-400 transition-colors">
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-medium">Best Value</span>
          </div>

          <h3 class="text-2xl font-bold text-gray-900 mb-4">Large</h3>
          <div class="mb-6">
            <div class="text-4xl font-bold text-purple-600 mb-2">$80</div>
            <p class="text-gray-600">1,000 Credits</p>
            <p class="text-sm text-green-600 font-medium mt-1">$0.08 per credit • Save 20%</p>
          </div>

          <div class="mb-8">
            <p class="text-gray-700 mb-4">Perfect for:</p>
            <ul class="text-sm text-gray-600 space-y-2">
              <li>• Large enterprises</li>
              <li>• Multiple campaigns</li>
              <li>• High-volume usage</li>
            </ul>
          </div>

          <a href="/tool/qr-code-generator" class="w-full bg-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-purple-700 transition-colors">
            Buy $80 Package
          </a>
        </div>
      </div>

      <!-- Custom Option -->
      <div class="mt-16 text-center">
        <div class="bg-gray-50 rounded-2xl p-8 max-w-2xl mx-auto">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Need More Credits?</h3>
          <p class="text-gray-600 mb-6">
            Contact us for custom packages with volume discounts for large organizations.
          </p>
          <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 bg-gray-900 text-white rounded-lg font-semibold hover:bg-gray-800 transition-colors">
            Contact Sales
          </a>
        </div>
      </div>
    </div>
  </section>



  <!-- What You Get -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-4xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          What You Get With Every Credit
        </h2>
        <p class="text-xl text-gray-600">
          All features included. No tiers, no limitations.
        </p>
      </div>

      <div class="bg-white rounded-2xl p-8 shadow-lg">
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">QR Code Features</h3>
            <ul class="space-y-4">
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Dynamic QR codes</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Custom design & colors</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Logo embedding</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">URL updates anytime</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Bulk download</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Analytics Features</h3>
            <ul class="space-y-4">
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7-293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Real-time scan tracking</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Geographic insights</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7-293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Device & OS data</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7-293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Time-based analytics</span>
              </li>
              <li class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7-293-7-293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Export to Excel</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Simple Examples -->
  <section class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Simple Examples
        </h2>
        <p class="text-xl text-gray-600">
          See exactly what you'll pay for real-world usage.
        </p>
      </div>

      <div class="grid md:grid-cols-2 gap-8">
        <div class="bg-blue-50 rounded-2xl p-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-6">Small Campaign</h3>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-700">10 QR codes</span>
              <span class="font-bold text-blue-600">$1.00</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-700">Up to 10,000 scans</span>
              <span class="font-bold text-green-600">Free</span>
            </div>
            <div class="border-t border-blue-200 pt-4 flex justify-between items-center">
              <span class="text-lg font-bold text-gray-900">Total Cost:</span>
              <span class="text-2xl font-bold text-blue-600">$1.00</span>
            </div>
          </div>
        </div>

        <div class="bg-purple-50 rounded-2xl p-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-6">Large Campaign</h3>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-700">100 QR codes</span>
              <span class="font-bold text-purple-600">$8.00</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-700">200,000 scans</span>
              <span class="font-bold text-purple-600">$10.00</span>
            </div>
            <div class="border-t border-purple-200 pt-4 flex justify-between items-center">
              <span class="text-lg font-bold text-gray-900">Total Cost:</span>
              <span class="text-2xl font-bold text-purple-600">$18.00</span>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-12 text-center">
        <div class="bg-gray-50 rounded-xl p-6 max-w-2xl mx-auto">
          <p class="text-gray-600">
            <strong>Remember:</strong> Each QR code includes 1,000 free scans.
            You only pay extra if you exceed this limit.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-4xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Common Questions
        </h2>
      </div>

      <div class="space-y-6">
        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            Do credits expire?
          </h3>
          <p class="text-gray-600">
            No, credits never expire. Buy them when you need them, use them when you want.
          </p>
        </div>

        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            What happens if I run out of credits?
          </h3>
          <p class="text-gray-600">
            Your existing QR codes continue to work normally. Just buy more credits when you need to create new ones.
          </p>
        </div>

        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            Are there any monthly fees?
          </h3>
          <p class="text-gray-600">
            No monthly fees, no subscriptions. You only pay for the credits you buy.
          </p>
        </div>

        <div class="bg-white rounded-xl p-6 shadow-sm">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            Can I get a refund?
          </h3>
          <p class="text-gray-600">
            Yes, we offer a 30-day money-back guarantee on all credit purchases.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-gradient-to-br from-blue-600 to-purple-600">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <h2 class="text-4xl font-bold text-white mb-6">
        Start Creating QR Codes Today
      </h2>
      <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
        Buy credits and start creating professional QR codes with analytics in minutes.
      </p>

      <a href="/tool/qr-code-generator" class="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-bold rounded-lg hover:bg-gray-100 transition-colors text-lg shadow-lg">
        Buy Credits & Start Now
      </a>

      <p class="text-blue-200 text-sm mt-6">
        No subscriptions • Credits never expire • 30-day money-back guarantee
      </p>
    </div>
  </section>

  <!-- Enterprise Contact -->
  <section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <div class="bg-gray-50 rounded-2xl p-8">
        <h3 class="text-2xl font-bold text-gray-900 mb-4">Need Enterprise Solutions?</h3>
        <p class="text-gray-600 mb-6">
          Custom features, dedicated support, and volume discounts for large organizations.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="mailto:<EMAIL>" class="bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors">
            Email: <EMAIL>
          </a>
          <div class="text-sm text-gray-500 flex items-center justify-center">
            Response within 24 hours
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>